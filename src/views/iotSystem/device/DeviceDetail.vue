<template>
  <div class="device-detail">
    <!-- 设备基本信息 -->

    <el-card class="device-info">
      <template #header>
        <div class="title">
          <h2>设备基本信息</h2>
          <Icon icon="ep:edit" :size="20" color="#409efc" class="edit-icon" @click="openEditform"
        /></div>
      </template>
      <el-skeleton :loading="isLoading" animated>
        <template #default>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="IMEI">
                <view class="device-name">
                  <span>{{ deviceInfo?.imei }}</span>
                  <el-tooltip content="告警中" placement="top" v-if="deviceInfo?.alerts">
                    <Icon icon="ep:warn-triangle-filled" :size="20" color="red" />
                  </el-tooltip>
                </view>
              </el-form-item>
              <el-form-item label="设备名称">
                <span>{{ deviceInfo?.name }}</span>
              </el-form-item>
              <el-form-item label="CCID">
                <span>{{ deviceInfo?.ccid }}</span>
              </el-form-item>
              <el-form-item label="产品名称">
                <span>{{ deviceInfo?.productName }}</span>
              </el-form-item>
              <el-form-item label="设备已用流量">
                <span>{{ deviceInfo?.waft }}</span>
              </el-form-item>
              <el-form-item label="信号强度">
                <span>{{ deviceInfo?.waft }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备状态">
                <dict-tag :type="DICT_TYPE.IOT_DEVICE_STATE" :value="deviceInfo?.status" />
              </el-form-item>

              <el-form-item label="固件版本">
                <span>{{ deviceInfo?.version }}</span>
              </el-form-item>
              <el-form-item label="卡激活时间">
                <span>{{ deviceInfo?.activeTime }}</span>
              </el-form-item>
              <el-form-item label="卡到期时间">
                <span>{{ deviceInfo?.expireTime }}</span>
              </el-form-item>
              <el-form-item label="最近在线时间">
                <span>{{ deviceInfo?.recentTime }}</span>
              </el-form-item>
              <el-form-item label="备注">
                <span>{{ deviceInfo?.remark }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-skeleton>
    </el-card>

    <!-- 当前数据 -->
    <el-card class="current-data">
      <template #header>
        <h2>当前数据</h2>
      </template>
      <el-skeleton :loading="isLoading" animated>
        <template #default>
          <div class="data-container">
            <el-card
              v-for="(item, index) in deviceInfo?.realTimeData"
              :key="index"
              class="single-data"
            >
              <div class="data-title">
                {{ item.dataType }}
              </div>
              <div class="data-value">
                <el-text type="primary"> {{ item.dataValue }}</el-text>
              </div>
            </el-card>
          </div>
        </template>
      </el-skeleton>
    </el-card>
    <!-- 历史数据 -->
    <el-card class="history-data">
      <template #header>
        <h2>历史数据</h2>
      </template>
      <div class="query-section">
        <el-date-picker
          v-model="queryParam.queryTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
        />
        <el-button type="primary" @click="fetchHistoryData">查询</el-button>
        <el-button type="primary" @click="reSetQuery">重置</el-button>
      </div>
      <el-table :data="historyData" border :loading="historyLoading" empty-text="暂无历史数据">
        <el-table-column prop="upTime" label="时间" />
        <el-table-column prop="dataType" label="类型" />
        <el-table-column prop="dataValue" label="数据" />
      </el-table>
      <el-pagination
        v-model:current-page="queryParam.pageNo"
        :total="total"
        @change="fetchHistoryData"
        layout="total, prev, pager, next"
      />
    </el-card>
    <!-- 表单弹窗：添加/修改 -->
    <EditDeviceForm ref="formRef" @success="fetchData" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import EditDeviceForm from './EditDeviceForm.vue'
import { useRoute } from 'vue-router'
import { IotDataDeviceApi, IotDataDeviceVO } from '@/api/iotSystem/device'
import { formatDate } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import dayjs from 'dayjs'
import { formatToDate } from '@/utils/dateUtil'
import { omitBy, identity, omit } from 'lodash-es'

const DEFAULT_SEARCH_TIME = [
  formatToDate(dayjs().subtract(1, 'month')) + ' 00:00:00',
  formatToDate(dayjs()) + ' 23:59:59'
]
const route = useRoute()

// 加载状态
const isLoading = ref(false)
// 设备信息
const deviceInfo = ref()
// 模拟数据请求
const fetchData = async () => {
  isLoading.value = true
  try {
    const id = route.query.id as unknown as number
    const data = await IotDataDeviceApi.getIotDataDevice(id)
    deviceInfo.value = {
      ...data,
      activeTime: formatDate(data.activeTime),
      expireTime: formatDate(data.expireTime),
      recentTime: formatDate(data.recentTime)
    }
  } finally {
    isLoading.value = false
  }
}

const historyLoading = ref(false)
// 历史数据
const historyData = ref([])
// 查询时间范围
const queryParam = ref<{
  imei: number
  queryTimeRange: string[]
  pageSize: number
  pageNo: number
}>({
  imei: route.query.imei as unknown as number,
  queryTimeRange: DEFAULT_SEARCH_TIME,
  pageSize: 10,
  pageNo: 1
})
// 总数据量
const total = ref(0)

const fetchHistoryData = async () => {
  historyLoading.value = true
  try {
    let params: any = {
      ...queryParam.value,
      startTime: queryParam.value?.queryTimeRange?.[0],
      endTime: queryParam.value?.queryTimeRange?.[1]
    }

    // 清除为空的值
    omitBy(params, identity)
    // 移除 queryTimeRange 属性
    params = omit(params, 'queryTimeRange')

    const data = await IotDataDeviceApi.getDeviceHistoryData(params)
    historyData.value = data.list
    total.value = data.total
  } finally {
    historyLoading.value = false
  }
}
const reSetQuery = () => {
  queryParam.value.queryTimeRange = DEFAULT_SEARCH_TIME
  fetchHistoryData()
}

// 页面挂载时请求数据
onMounted(() => {
  fetchData()
  fetchHistoryData()
})

/** 添加/修改操作 */
const formRef = ref()

const openEditform = () => {
  formRef.value.open(deviceInfo.value)
}
</script>

<style lang="scss" scoped>
.device-detail {
  padding: 20px;
  margin: 0 auto;
}

.device-info {
  .title {
    display: flex;
    gap: 10px;
    justify-content: start;
    align-items: center;
  }

  :deep(.device-name) {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .device-name {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .edit-icon {
    cursor: pointer;
  }
}

.data-container {
  display: flex;
  gap: 30px;
  justify-content: space-between;

  .single-data {
    flex: 1;
  }

  .data-title {
    margin-bottom: 18px;
    font-size: 18px;
    font-weight: bold;
  }

  .data-value {
    padding: 20px 0 40px;
    text-align: center;

    .el-text {
      font-size: 44px;
      font-weight: bolder;
    }
  }
}

.device-info,
.current-data,
.history-data {
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);

  :deep(.el-pagination) {
    display: flex;
    justify-content: right;
    margin-top: 10px;
  }
}

.query-section {
  display: flex;
  width: 400px;
  margin-bottom: 20px;
  gap: 10px;
}
</style>
