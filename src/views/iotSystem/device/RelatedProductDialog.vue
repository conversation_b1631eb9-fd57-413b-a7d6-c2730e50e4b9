<template>
  <Dialog title="关联产品" v-model="dialogVisible">
    <el-select
      v-model="productId"
      filterable
      placeholder="请输入产品名称搜索产品"
      :loading="loading"
      remote-show-suffix
      @change="selectProduct"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading || !productId"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IotDataProductApi } from '@/api/iotSystem/product'
import { IotDataDeviceApi, IotDataDeviceVO } from '@/api/iotSystem/device'

const message = useMessage() // 消息弹窗

const formLoading = ref(false)
const dialogVisible = ref(false)
const productId = ref()
const options = ref()
const loading = ref(false)
const device = ref<IotDataDeviceVO | null>()

/** 打开弹窗 */
const open = async (deviceInfo: IotDataDeviceVO) => {
  dialogVisible.value = true
  device.value = deviceInfo
  getProductOptions()
  productId.value = deviceInfo.productId
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const getProductOptions = async () => {
  const result = await IotDataProductApi.getIotDataProductPage({
    pageNo: 1,
    pageSize: 100
  })
  options.value = result.list.map((item) => ({ label: item.name, value: item.id }))
}

const selectProduct = (e) => {
  const productName = options.value.find((item) => item.value === e).label
  device.value = { ...device.value, productName } as unknown as IotDataDeviceVO
}

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 关联产品 */
const submitForm = async () => {
  formLoading.value = true
  try {
    await message.confirm('确认要关联当前产品吗?')
    await IotDataDeviceApi.updateIotDataDevice({
      ...device.value,
      productId: productId.value
    } as unknown as IotDataDeviceVO)
    message.success('修改成功！')
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
