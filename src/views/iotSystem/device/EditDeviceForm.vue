<template>
  <Dialog title="编辑设备" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入设备名称" />
      </el-form-item>

      <el-form-item label="CCID" prop="ccid">
        <el-input v-model="formData.ccid" placeholder="请输入CCID" />
      </el-form-item>
      <el-form-item label="固件版本" prop="version">
        <el-input v-model="formData.version" placeholder="请输入固件版本" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IotDataDeviceApi, IotDataDeviceVO } from '@/api/iotSystem/device'

/** 设备 表单 */
defineOptions({ name: 'IotDataDeviceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const formData = ref<IotDataDeviceVO>({
  id: undefined,
  name: undefined,
  imei: undefined,
  ccid: undefined,
  remark: undefined,
  version: undefined
} as unknown as IotDataDeviceVO)
const formRules = reactive({
  name: [{ required: true, message: '设备名称不能为空', trigger: 'blur' }],
  ccid: [{ required: true, message: 'CCID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (deviceInfo: IotDataDeviceVO) => {
  resetForm()
  dialogVisible.value = true
  formData.value = { ...deviceInfo }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IotDataDeviceVO
    await IotDataDeviceApi.updateIotDataDevice(data)
    message.success(t('common.updateSuccess'))

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    imei: undefined,
    ccid: undefined,
    remark: undefined,
    version: undefined
  } as unknown as IotDataDeviceVO
  formRef.value?.resetFields()
}
</script>
