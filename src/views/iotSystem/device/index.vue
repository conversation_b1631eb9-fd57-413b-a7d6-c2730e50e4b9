<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="86px"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="IMEI" prop="imei">
        <el-input
          v-model="queryParams.imei"
          placeholder="请输入IMEI"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="CCID" prop="ccid">
        <el-input
          v-model="queryParams.ccid"
          placeholder="请输入CCID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择设备状态"
          clearable
          class="!w-240px"
        >
          <template v-for="dict in getIntDictOptions(DICT_TYPE.IOT_DEVICE_STATE)" :key="dict.value">
            <el-option :label="dict.label" :value="dict.value" v-if="dict.label === '禁用'" />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="卡激活时间" prop="activeTime">
        <el-date-picker
          v-model="queryParams.activeTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="卡到期时间" prop="expireTime">
        <el-date-picker
          v-model="queryParams.expireTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否告警" prop="alerts">
        <el-select
          v-model="queryParams.alerts"
          placeholder="请选择是否告警"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.IOT_ALERT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="IMEI" align="center" prop="imei" min-width="150px">
        <template #default="scope">
          <RouterLink
            :to="`/iotSystem/device/detail?id=${scope.row.id}&imei=${scope.row.imei}`"
            class="deatil-link"
          >
            <el-text type="primary">{{ scope.row.imei }}</el-text>
          </RouterLink>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center" prop="name" min-width="120px"/>

      <el-table-column label="产品名称" align="center" prop="productName" min-width="130px"/>
      <el-table-column label="CCID" align="center" prop="ccid" />
      <el-table-column label="设备状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.IOT_DEVICE_STATE" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="卡激活时间"
        align="center"
        prop="activeTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="卡到期时间"
        align="center"
        prop="expireTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="最近在线时间"
        align="center"
        prop="recentTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="已用流量" align="center" prop="flow" />
      <el-table-column label="信号强度" align="center" prop="waft" />
      <el-table-column label="是否告警" align="center" prop="alerts">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.IOT_ALERT_STATUS" :value="scope.row.alerts" /> </template
      ></el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />

      <el-table-column label="操作" align="center" min-width="220px" fixed="right">
        <template #default="scope">
          <!-- <div class="operation"> -->
          <el-button
            link
            type="primary"
            v-hasPermi="['iotSystem:device:update']"
            @click="openRelatedProductDialog(scope.row)"
          >
            关联产品
          </el-button>
          <el-button
            link
            type="danger"
            @click="handlePrphibited(scope.row)"
            v-hasPermi="['iotSystem:device:update']"
          >
            {{ scope.row.status === IotDeviceStatus.PROHIBITED ? '解禁' : '禁用' }}
          </el-button>
          <el-button
            link
            type="danger"
            v-if="scope.row.alerts"
            @click="handleAlerts(scope.row)"
            v-hasPermi="['iotSystem:device:update']"
          >
            关闭告警
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <RelatedProductDialog ref="relatedRef" @success="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { IotDataDeviceApi, IotDataDeviceVO } from '@/api/iotSystem/device'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import RelatedProductDialog from './RelatedProductDialog.vue'
import { IotDeviceStatus, IotDeviceAlerts } from '@/utils/constants'
import { useRoute } from 'vue-router'

const route = useRoute()
/** 设备 列表 */
defineOptions({ name: 'IotDataDevice' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref<IotDataDeviceVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  imei: undefined,
  productId: undefined,
  productName: route.query.productName ?? undefined,
  ccid: undefined,
  status: undefined,
  activeTime: [],
  expireTime: [],
  recentTime: [],
  flow: undefined,
  waft: undefined,
  alerts: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await IotDataDeviceApi.getIotDataDevicePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/**关联产品按钮 */
const relatedRef = ref()
const openRelatedProductDialog = (deviceInfo: IotDataDeviceVO) => {
  relatedRef.value.open(deviceInfo)
}

/** 禁用产品 */
const handlePrphibited = async (deviceInfo: IotDataDeviceVO) => {
  const messageContent =
    deviceInfo.status === IotDeviceStatus.PROHIBITED
      ? '是否确认解禁所选中设备?'
      : '是否确认禁用所选中设备？'
  await message.confirm(messageContent)
  await IotDataDeviceApi.updateIotDataDevice({
    ...deviceInfo,
    status:
      deviceInfo.status === IotDeviceStatus.PROHIBITED
        ? IotDeviceStatus.OFFLINE
        : IotDeviceStatus.PROHIBITED
  } as unknown as IotDataDeviceVO)
  message.success('修改成功！')
  await getList()
}
/** 解除告警操作 */
const handleAlerts = async (deviceInfo: IotDataDeviceVO) => {
  await message.confirm('是否确认关闭当前设备告警')
  await IotDataDeviceApi.updateIotDataDevice({
    ...deviceInfo,
    alerts: IotDeviceAlerts.NONE
  } as unknown as IotDataDeviceVO)
  message.success('修改成功！')
  await getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.deatil-link {
  text-decoration: none;
}

.operation {
  display: flex;
  // justify-content: flex-start;
}
</style>
