<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="86px"
    >
      <el-form-item label="CCID" prop="ccid">
        <el-input
          v-model="queryParams.ccid"
          placeholder="请输入CCID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="预警类别" prop="categoryId">
        <el-select
          v-model="queryParams.categoryId"
          placeholder="请选择预警类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.IOT_ALERT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker
          v-model="queryParams.expireTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="CCID" align="center" prop="ccid" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="预警类别" align="center" prop="category" />
      <el-table-column
        label="卡到期时间"
        align="center"
        prop="expireTime"
        :formatter="dateFormatter"
      />
      <el-table-column label="流量使用情况" align="center" prop="flowRate">
        <template #default="scope">
          <el-progress :percentage="scope.row.flowRate" :color="customColors" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { IotAlertsApi, IotAlertsVO } from '@/api/iotSystem/alerts'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { isNil } from 'lodash-es'
import { formatToDate } from '@/utils/dateUtil'
import dayjs from 'dayjs'
/** 告警 列表 */
defineOptions({ name: 'IotAlerts' })

const loading = ref(true) // 列表的加载中
const list = ref<IotAlertsVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orgId: undefined,
  deviceId: undefined,
  deviceName: undefined,
  ccid: undefined,
  categoryId: undefined,
  category: undefined,
  expireTime: [
    formatToDate(dayjs().subtract(1, 'month')) + ' 00:00:00',
    formatToDate(dayjs()) + ' 23:59:59'
  ],
  usedFlow: undefined,
  allFlow: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await IotAlertsApi.getIotAlertsPage(queryParams)
    list.value = data.list
    list.value = list.value.map((item) => {
      item.flowRate =
        !isNil(item.usedFlow) && !isNil(item.allFlow) && item.allFlow !== 0
          ? parseFloat(((item.usedFlow / item.allFlow) * 100).toFixed(2))
          : 0
      return item
    })
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const customColors = [
  { color: '#f56c6c', percentage: 80 },
  { color: '#e6a23c', percentage: 60 },
  { color: '#5cb87a', percentage: 40 },
  { color: '#1989fa', percentage: 20 }
]
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
