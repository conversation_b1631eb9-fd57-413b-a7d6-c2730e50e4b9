<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="产品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品型号" prop="model">
        <el-input
          v-model="queryParams.model"
          placeholder="请输入产品型号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品类别" prop="categoryId">
        <el-select
          v-model="queryParams.categoryId"
          placeholder="请选择产品类别"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.IOT_PRODUCT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品厂家" prop="factory">
        <el-input
          v-model="queryParams.factory"
          placeholder="请输入产品厂家"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['iotSystem:product:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="产品名称" align="center" prop="name" min-width="120px"/>
      <el-table-column label="产品型号" align="center" prop="model" />
      <el-table-column label="产品类别" align="center" prop="category" min-width="100px"/>
      <el-table-column label="产品厂家" align="center" prop="factory" />
      <el-table-column label="账号/密码" align="center" prop="account">
        <template #default="scope">
          <span>{{ `${scope.row.account}/${scope.row.password}` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备统计" align="center" prop="count">
        <template #default="scope">
          <RouterLink
            :to="`/iotSystem/device?productName=${scope.row.name}`"
            class="count-link"
            v-if="scope.row.count"
          >
            <el-text type="primary">{{ scope.row.count }}</el-text></RouterLink
          >
          <span v-else>0</span>
        </template>
      </el-table-column>
      <el-table-column label="物联卡有效时长(天)" align="center" prop="remain" min-width="100px" />
      <el-table-column label="物联卡计费周期(天)" align="center" prop="period" min-width="100px" />
      <el-table-column label="周期内可用流量" align="center" prop="flow" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['iotSystem:product:edit']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['iotSystem:product:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <EditProductForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { IotDataProductApi, IotDataProductVO } from '@/api/iotSystem/product'
import EditProductForm from './EditProductDialog.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 产品 列表 */
defineOptions({ name: 'IotDataProduct' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<IotDataProductVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  model: undefined,
  categoryId: undefined,
  category: undefined,
  factory: undefined,
  account: undefined,
  password: undefined,
  count: undefined,
  remain: undefined,
  period: undefined,
  flow: undefined,
  remark: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await IotDataProductApi.getIotDataProductPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await IotDataProductApi.deleteIotDataProduct(id)
  message.success(t('common.delSuccess'))
  // 刷新列表
  await getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.count-link {
  text-decoration: none;
}
</style>
